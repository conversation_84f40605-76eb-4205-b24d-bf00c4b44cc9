import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { UserRole } from "src/common/enums/user-role.enum";

// Import the actual DTOs from their respective files
import { LoginDto as LoginDtoClass } from "./login.dto";
import { RegisterDto as RegisterDtoClass } from "./register.dto";

// Swagger documentation for LoginDto
export class LoginDto implements LoginDtoClass {
  @ApiPropertyOptional({ example: "<EMAIL>" })
  email?: string;

  @ApiPropertyOptional({ example: "+1234567890" })
  phone?: string;

  @ApiProperty({ example: "Password123!" })
  password!: string;
}

// Swagger documentation for RegisterDto
export class RegisterDto implements RegisterDtoClass {
  @ApiProperty({ example: "John Doe" })
  name!: string;

  @ApiPropertyOptional({ example: "<EMAIL>" })
  email?: string;

  @ApiPropertyOptional({ example: "+1234567890" })
  phone?: string;

  @ApiProperty({ example: "Password123!" })
  password!: string;
}

export class OtpRequestDto {
  @ApiProperty({ example: "+1234567890" })
  phone!: string;
}

export class OtpVerifyDto {
  @ApiProperty({ example: "+1234567890" })
  phone!: string;

  @ApiProperty({ example: "123456" })
  code!: string;
}

export class UserResponseDto {
  @ApiProperty({ example: "550e8400-e29b-41d4-a716-446655440000" })
  id!: string;

  @ApiProperty({ example: "John Doe" })
  name!: string;

  @ApiProperty({ example: "<EMAIL>" })
  email!: string;

  @ApiProperty({ example: "+1234567890" })
  phone!: string;

  @ApiProperty({ example: "worker" })
  role!: string;
}

export class TokenResponseDto {
  @ApiProperty({ example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." })
  access_token!: string;
}

export class AuthResponseDto {
  @ApiProperty({ example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." })
  access_token!: string;

  @ApiProperty({ example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." })
  token!: string;

  @ApiProperty({ example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." })
  refreshToken!: string;

  @ApiProperty({
    type: UserResponseDto,
    example: {
      id: "550e8400-e29b-41d4-a716-446655440000",
      name: "John Doe",
      email: "<EMAIL>",
      phone: "+1234567890",
      role: "worker",
    },
  })
  user!: UserResponseDto;
}

export class OtpResponseDto {
  @ApiProperty({ example: "OTP sent successfully" })
  message!: string;
}

export class OtpVerifyResponseDto extends AuthResponseDto {
  @ApiProperty({ example: false })
  isNewUser!: boolean;
}
